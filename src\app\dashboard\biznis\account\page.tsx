'use client';

import { useEffect, useState, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { TabsWithBadge } from '@/components/ui/tabs-with-badge';
import { CountrySelector } from '@/components/ui/country-selector';
import { useAuth } from '@/contexts/AuthContext';
import {
  getProfile,
  updateProfile,
  getBusiness,
  updateBusiness,
} from '@/lib/profiles';
import { supabase } from '@/lib/supabase';
import {
  Loader2,
  Save,
  User,
  MapPin,
  CreditCard,
  Shield,
  Crown,
  Settings,
} from 'lucide-react';
import { toast } from 'sonner';
import Link from 'next/link';
import { SubscriptionType } from '@/lib/subscriptions';

const accountSchema = z.object({
  full_name: z.string().min(2, 'Ime i prezime mora imati najmanje 2 karaktera'),
  company_name: z.string().optional(), // Company name is readonly, no validation needed
  email: z.string().email('Neispravna email adresa'),
  phone: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  postal_code: z.string().optional(),
  country: z.string().optional(),
  tax_id: z.string().optional(),
  bank_account: z.string().optional(),
  bank_name: z.string().optional(),
});

type AccountForm = z.infer<typeof accountSchema>;

export default function BusinessAccountPage() {
  // Mapiranje između punih imena zemalja i kodova za CountrySelector
  const countryNameToCode = (countryName: string): string => {
    const countryMap: { [key: string]: string } = {
      Albanija: 'albania',
      'Bosna i Hercegovina': 'bosnia-herzegovina',
      Bugarska: 'bulgaria',
      'Crna Gora': 'montenegro',
      Grčka: 'greece',
      Hrvatska: 'croatia',
      Kosovo: 'kosovo',
      'Sjeverna Makedonija': 'north-macedonia',
      Rumunija: 'romania',
      Srbija: 'serbia',
      Slovenija: 'slovenia',
    };
    return countryMap[countryName] || '';
  };

  const countryCodeToName = (countryCode: string): string => {
    const codeMap: { [key: string]: string } = {
      albania: 'Albanija',
      'bosnia-herzegovina': 'Bosna i Hercegovina',
      bulgaria: 'Bugarska',
      montenegro: 'Crna Gora',
      greece: 'Grčka',
      croatia: 'Hrvatska',
      kosovo: 'Kosovo',
      'north-macedonia': 'Sjeverna Makedonija',
      romania: 'Rumunija',
      serbia: 'Srbija',
      slovenia: 'Slovenija',
    };
    return codeMap[countryCode] || countryCode;
  };
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [, setProfile] = useState<{
    id: string;
    full_name?: string;
    email?: string;
    phone?: string;
    city?: string;
    country?: string;
  } | null>(null);
  const [business, setBusiness] = useState<{
    company_name?: string;
    address?: string;
    postal_code?: string;
    tax_id?: string;
    bank_account?: string;
    bank_name?: string;
    subscription_type?: SubscriptionType;
  } | null>(null);
  const [activeTab, setActiveTab] = useState('osnovne-informacije');

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm<AccountForm>({
    resolver: zodResolver(accountSchema),
  });

  useEffect(() => {
    if (user) {
      loadProfile();
    }
  }, [user, loadProfile]);

  const loadProfile = useCallback(async () => {
    try {
      setLoading(true);

      // Load profile data
      const { data: profileData, error: profileError } = await getProfile(
        user!.id
      );
      if (profileError || !profileData) {
        toast.error('Greška pri učitavanju profila');
        return;
      }
      setProfile(profileData);

      // Load business data
      const { data: businessData, error: businessError } = await getBusiness(
        user!.id
      );
      if (businessError || !businessData) {
        toast.error('Greška pri učitavanju business podataka');
        return;
      }
      // Check subscription status from user_subscriptions table
      const { data: subscription } = await supabase
        .from('user_subscriptions')
        .select('status')
        .eq('user_id', user!.id)
        .eq('user_type', 'business')
        .eq('status', 'active')
        .single();

      setBusiness({ ...businessData, hasActiveSubscription: !!subscription });

      // Get company name and original contact person name from user metadata (from registration)
      const {
        data: { user: authUser },
      } = await supabase.auth.getUser();
      const companyNameFromMeta = authUser?.user_metadata?.company_name || '';
      const originalContactPersonName =
        authUser?.user_metadata?.full_name || '';

      // Popuni formu sa postojećim podacima
      reset({
        full_name:
          businessData.contact_person_name ||
          originalContactPersonName ||
          profileData.full_name ||
          '',
        company_name: companyNameFromMeta,
        email: user!.email || '',
        phone: profileData.phone || '',
        address: profileData.address || '',
        city: profileData.city || '',
        postal_code: profileData.postal_code || '',
        country: countryNameToCode(profileData.country || ''), // Convert country name to code
        tax_id: profileData.tax_id || '',
        bank_account: profileData.bank_account || '',
        bank_name: profileData.bank_name || '',
      });
    } catch {
      toast.error('Neočekivana greška');
    } finally {
      setLoading(false);
    }
  }, [user, reset]);

  const onSubmit = async (data: AccountForm) => {
    if (!user) return;

    setSaving(true);
    try {
      // Update profile data
      const { error: profileError } = await updateProfile(user.id, {
        full_name: data.full_name,
        phone: data.phone || null,
        address: data.address || null,
        city: data.city || null,
        postal_code: data.postal_code || null,
        country: countryCodeToName(data.country || ''), // Convert country code to name
        tax_id: data.tax_id || null,
        bank_account: data.bank_account || null,
        bank_name: data.bank_name || null,
      });

      if (profileError) {
        toast.error('Greška pri ažuriranju profila');
        return;
      }

      // Update business contact person name
      const { error: businessError } = await updateBusiness(user.id, {
        contact_person_name: data.full_name,
      });

      if (businessError) {
        toast.error('Greška pri ažuriranju business podataka');
        return;
      }

      // Note: Company name is stored in user metadata and cannot be changed here
      // Only the business record creation during onboarding updates businesses.company_name
      // which now stores the brand name instead

      toast.success('Račun je uspješno ažuriran');
      loadProfile(); // Refresh data
    } catch {
      toast.error('Neočekivana greška');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout requiredUserType="business">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardLayout>
    );
  }

  const subscriptionType: SubscriptionType = business?.hasActiveSubscription
    ? 'premium'
    : 'free';

  const tabs = [
    {
      name: 'Osnovne informacije',
      value: 'osnovne-informacije',
      icon: <Settings className="h-4 w-4" />,
    },
    {
      name: 'Sigurnost',
      value: 'sigurnost',
      icon: <Shield className="h-4 w-4" />,
    },
    {
      name: 'Subskripcija',
      value: 'subskripcija',
      icon: <Crown className="h-4 w-4" />,
    },
  ];

  return (
    <DashboardLayout requiredUserType="business">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
            Moj račun
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Upravljajte svojim privatnim podacima i postavkama računa
          </p>
        </div>

        <TabsWithBadge
          tabs={tabs}
          value={activeTab}
          onValueChange={setActiveTab}
        >
          {/* Osnovne informacije Tab */}
          {activeTab === 'osnovne-informacije' && (
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 mt-6">
              {/* Osnovni podaci */}
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <User className="h-5 w-5 text-purple-500" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Osnovni podaci
                    </h3>
                  </div>
                  <p className="text-gray-600 dark:text-gray-400 text-sm mb-6">
                    Vaši lični podaci - ove informacije su privatne
                  </p>

                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label
                          htmlFor="full_name"
                          className="text-gray-900 dark:text-gray-100"
                        >
                          Ime i prezime *
                        </Label>
                        <Input
                          id="full_name"
                          {...register('full_name')}
                          placeholder="Marko Marković"
                          className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                        />
                        {errors.full_name && (
                          <p className="text-sm text-red-600 mt-1">
                            {errors.full_name.message}
                          </p>
                        )}
                      </div>

                      <div>
                        <Label
                          htmlFor="company_name"
                          className="text-gray-900 dark:text-gray-100"
                        >
                          Naziv kompanije
                        </Label>
                        <Input
                          id="company_name"
                          {...register('company_name')}
                          placeholder="Vaša kompanija d.o.o."
                          disabled
                          className="bg-gray-100/60 dark:bg-gray-700/40 border-purple-200/50"
                        />
                        <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                          Naziv kompanije se ne može mijenjati
                        </p>
                      </div>
                    </div>

                    <div>
                      <Label
                        htmlFor="email"
                        className="text-gray-900 dark:text-gray-100"
                      >
                        Email adresa
                      </Label>
                      <Input
                        id="email"
                        type="email"
                        {...register('email')}
                        disabled
                        className="bg-gray-100/60 dark:bg-gray-700/40 border-purple-200/50"
                      />
                      <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        Email se ne može mijenjati
                      </p>
                    </div>

                    <div>
                      <Label
                        htmlFor="phone"
                        className="text-gray-900 dark:text-gray-100"
                      >
                        Telefon
                      </Label>
                      <Input
                        id="phone"
                        {...register('phone')}
                        placeholder="+387 60 123 456"
                        className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Adresa */}
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <MapPin className="h-5 w-5 text-purple-500" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Adresa
                    </h3>
                  </div>
                  <p className="text-gray-600 dark:text-gray-400 text-sm mb-6">
                    Vaša adresa za fakturiranje i poslovnu korespondenciju
                  </p>

                  <div className="space-y-4">
                    <div>
                      <Label
                        htmlFor="address"
                        className="text-gray-900 dark:text-gray-100"
                      >
                        Ulica i broj
                      </Label>
                      <Input
                        id="address"
                        {...register('address')}
                        placeholder="Zmaja od Bosne 8"
                        className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <Label
                          htmlFor="city"
                          className="text-gray-900 dark:text-gray-100"
                        >
                          Grad
                        </Label>
                        <Input
                          id="city"
                          {...register('city')}
                          placeholder="Sarajevo"
                          className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                        />
                      </div>

                      <div>
                        <Label
                          htmlFor="postal_code"
                          className="text-gray-900 dark:text-gray-100"
                        >
                          Poštanski broj
                        </Label>
                        <Input
                          id="postal_code"
                          {...register('postal_code')}
                          placeholder="71000"
                          className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                        />
                      </div>

                      <div>
                        <Label
                          htmlFor="country"
                          className="text-gray-900 dark:text-gray-100"
                        >
                          Država
                        </Label>
                        <CountrySelector
                          value={watch('country')}
                          onValueChange={value => setValue('country', value)}
                          placeholder="Izaberite državu..."
                        />
                        {errors.country && (
                          <p className="text-sm text-red-600 mt-1">
                            {errors.country.message}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Finansijski podaci */}
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <CreditCard className="h-5 w-5 text-purple-500" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Finansijski podaci
                    </h3>
                  </div>
                  <p className="text-gray-600 dark:text-gray-400 text-sm mb-6">
                    Podaci potrebni za fakturiranje i plaćanja
                  </p>

                  <div className="space-y-4">
                    <div>
                      <Label
                        htmlFor="tax_id"
                        className="text-gray-900 dark:text-gray-100"
                      >
                        PDV broj / JIB
                      </Label>
                      <Input
                        id="tax_id"
                        {...register('tax_id')}
                        placeholder="*********"
                        className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label
                          htmlFor="bank_account"
                          className="text-gray-900 dark:text-gray-100"
                        >
                          Broj računa
                        </Label>
                        <Input
                          id="bank_account"
                          {...register('bank_account')}
                          placeholder="*********0123456"
                          className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                        />
                      </div>

                      <div>
                        <Label
                          htmlFor="bank_name"
                          className="text-gray-900 dark:text-gray-100"
                        >
                          Naziv banke
                        </Label>
                        <Input
                          id="bank_name"
                          {...register('bank_name')}
                          placeholder="UniCredit Bank"
                          className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Save Button */}
              <div className="flex justify-end">
                <button
                  type="submit"
                  disabled={saving}
                  className="flex items-center gap-2 px-6 py-3 text-sm font-medium text-white bg-gradient-to-r from-purple-400 via-pink-400 to-purple-500 hover:from-purple-500 hover:via-pink-500 hover:to-purple-600 rounded-lg transition-all duration-200 hover:shadow-lg disabled:opacity-50"
                >
                  {saving ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4" />
                  )}
                  Sačuvaj promjene
                </button>
              </div>
            </form>
          )}

          {/* Sigurnost Tab */}
          {activeTab === 'sigurnost' && (
            <div className="mt-6">
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <Shield className="h-5 w-5 text-purple-500" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Sigurnost
                    </h3>
                  </div>
                  <p className="text-gray-600 dark:text-gray-400 text-sm mb-6">
                    Postavke sigurnosti vašeg računa
                  </p>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-white/60 dark:bg-gray-800/40 border border-purple-100/50 dark:border-purple-800/30 rounded-lg">
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-gray-100">
                          Promjena lozinke
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          Ažurirajte svoju lozinku redovno radi sigurnosti
                        </p>
                      </div>
                      <Link href="/promjena-lozinke">
                        <Button
                          variant="outline"
                          type="button"
                          className="bg-white/70 dark:bg-gray-800/50 hover:bg-white dark:hover:bg-gray-800/70 border-gray-200/50"
                        >
                          Promijeni lozinku
                        </Button>
                      </Link>
                    </div>

                    <div className="flex items-center justify-between p-4 bg-white/60 dark:bg-gray-800/40 border border-purple-100/50 dark:border-purple-800/30 rounded-lg">
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-gray-100">
                          Dvofaktorska autentifikacija
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          Dodajte dodatni sloj sigurnosti vašem računu
                        </p>
                      </div>
                      <Button
                        variant="outline"
                        type="button"
                        className="bg-white/70 dark:bg-gray-800/50 hover:bg-white dark:hover:bg-gray-800/70 border-gray-200/50"
                      >
                        Podesi 2FA
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Subskripcija Tab */}
          {activeTab === 'subskripcija' && (
            <div className="mt-6">
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <Crown className="h-5 w-5 text-purple-500" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Subskripcija
                    </h3>
                  </div>
                  <p className="text-gray-600 dark:text-gray-400 text-sm mb-6">
                    Vaš trenutni plan i mogućnosti nadogradnje
                  </p>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-white/60 dark:bg-gray-800/40 border border-purple-100/50 dark:border-purple-800/30 rounded-lg">
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-gray-100 capitalize">
                          Trenutni plan:{' '}
                          {subscriptionType === 'free' ? 'Free' : 'Premium'}
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {subscriptionType === 'free'
                            ? 'Osnovne funkcionalnosti sa ograničenjima'
                            : 'Sve premium funkcionalnosti uključene'}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        {subscriptionType === 'free' ? (
                          <Button
                            variant="outline"
                            type="button"
                            className="bg-gradient-to-r from-purple-400 via-pink-400 to-purple-500 hover:from-purple-500 hover:via-pink-500 hover:to-purple-600 text-white border-none"
                            onClick={() => {
                              window.location.href =
                                '/dashboard/biznis/packages';
                            }}
                          >
                            <Crown className="h-4 w-4 mr-2" />
                            Nadogradi na Premium
                          </Button>
                        ) : (
                          <div className="flex items-center gap-2 px-3 py-2 bg-gradient-to-r from-yellow-400 to-yellow-500 text-white rounded-lg">
                            <Crown className="h-4 w-4" />
                            <span className="font-medium">Premium</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {subscriptionType === 'free' && (
                      <div className="p-4 bg-white/60 dark:bg-gray-800/40 border border-purple-100/50 dark:border-purple-800/30 rounded-lg">
                        <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                          Premium prednosti
                        </h4>
                        <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                          <li>• Neograničen broj aktivnih kampanja</li>
                          <li>• Pristup aplikacijama influencera</li>
                          <li>• Detaljni pregled aplikacija</li>
                          <li>• Prioritetna podrška</li>
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </TabsWithBadge>
      </div>
    </DashboardLayout>
  );
}
