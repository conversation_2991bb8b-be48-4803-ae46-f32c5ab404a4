/**
 * Application Startup Configuration
 * Handles environment validation and application initialization
 */

import { validateEnvironmentOnStartup, getEnvConfig } from './env-validation';

export interface StartupConfig {
  env: ReturnType<typeof getEnvConfig>;
  isInitialized: boolean;
  startupTime: Date;
}

let startupConfig: StartupConfig | null = null;

/**
 * Initialize the application with environment validation
 * This should be called once when the application starts
 */
export async function initializeApplication(): Promise<StartupConfig> {
  if (startupConfig) {
    return startupConfig;
  }

  const startTime = Date.now();
  console.log('🚀 Initializing Influencer Platform...');

  try {
    // 1. Validate environment variables
    const env = validateEnvironmentOnStartup();

    // 2. Log startup information
    console.log(`📋 Environment: ${env.NODE_ENV}`);
    console.log(`🌐 App URL: ${env.NEXT_PUBLIC_APP_URL}`);
    console.log(`🔑 Supabase URL: ${env.NEXT_PUBLIC_SUPABASE_URL}`);

    // 3. Initialize startup configuration
    startupConfig = {
      env,
      isInitialized: true,
      startupTime: new Date(),
    };

    // 4. Environment-specific initialization
    await initializeForEnvironment(env);

    const endTime = Date.now();
    console.log(
      `✅ Application initialized successfully in ${endTime - startTime}ms`
    );

    return startupConfig;
  } catch (error) {
    console.error('❌ Application initialization failed:', error);
    throw error;
  }
}

/**
 * Environment-specific initialization
 */
async function initializeForEnvironment(env: ReturnType<typeof getEnvConfig>) {
  switch (env.NODE_ENV) {
    case 'development':
      await initializeDevelopment(env);
      break;
    case 'production':
      await initializeProduction(env);
      break;
    case 'staging':
      await initializeStaging(env);
      break;
    default:
      console.warn(`⚠️  Unknown environment: ${env.NODE_ENV}`);
  }
}

/**
 * Development-specific initialization
 */
async function initializeDevelopment(env: ReturnType<typeof getEnvConfig>) {
  console.log('🛠️  Development mode initialized');

  // Log additional development info
  if (env.DEBUG === 'true') {
    console.log('🐛 Debug mode enabled');
  }

  if (env.DISABLE_EMAILS === 'true') {
    console.log('📧 Email sending disabled for development');
  }

  // Development-specific warnings
  if (env.STRIPE_SECRET_KEY?.includes('live')) {
    console.warn('⚠️  WARNING: Using live Stripe keys in development!');
  }
}

/**
 * Production-specific initialization
 */
async function initializeProduction(env: ReturnType<typeof getEnvConfig>) {
  console.log('🏭 Production mode initialized');

  // Production checks
  if (env.DEBUG === 'true') {
    console.warn('⚠️  WARNING: Debug mode is enabled in production');
  }

  if (env.STRIPE_SECRET_KEY?.includes('test')) {
    console.warn('⚠️  WARNING: Using test Stripe keys in production!');
  }

  if (!env.SENTRY_DSN) {
    console.warn(
      '⚠️  WARNING: Error tracking (Sentry) not configured for production'
    );
  }

  // Production optimizations
  console.log('🔧 Production optimizations applied');
}

/**
 * Staging-specific initialization
 */
async function initializeStaging() {
  console.log('🧪 Staging mode initialized');

  // Staging is typically used for testing production-like behavior
  // with development-like debugging capabilities
  console.log('🔍 Enhanced logging enabled for staging');
}

/**
 * Get the startup configuration
 * Will initialize if not already done
 */
export async function getStartupConfig(): Promise<StartupConfig> {
  if (!startupConfig) {
    return await initializeApplication();
  }
  return startupConfig;
}

/**
 * Check if application is initialized
 */
export function isApplicationInitialized(): boolean {
  return startupConfig?.isInitialized ?? false;
}

/**
 * Get application uptime in milliseconds
 */
export function getApplicationUptime(): number {
  if (!startupConfig) {
    return 0;
  }
  return Date.now() - startupConfig.startupTime.getTime();
}

/**
 * Health check function
 */
export async function healthCheck(): Promise<{
  status: 'healthy' | 'unhealthy';
  uptime: number;
  environment: string;
  timestamp: string;
  checks: Record<string, boolean>;
}> {
  const config = await getStartupConfig();
  const uptime = getApplicationUptime();

  // Perform various health checks
  const checks = {
    environmentValid: config.isInitialized,
    supabaseConfigured: !!(
      config.env.NEXT_PUBLIC_SUPABASE_URL &&
      config.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
    ),
    stripeConfigured: !!(
      config.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY &&
      config.env.STRIPE_SECRET_KEY
    ),
    webhookSecretsConfigured: !!(
      config.env.SUPABASE_WEBHOOK_SECRET && config.env.STRIPE_WEBHOOK_SECRET
    ),
  };

  const allChecksPass = Object.values(checks).every(check => check);

  return {
    status: allChecksPass ? 'healthy' : 'unhealthy',
    uptime,
    environment: config.env.NODE_ENV,
    timestamp: new Date().toISOString(),
    checks,
  };
}

/**
 * Graceful shutdown handler
 */
export function setupGracefulShutdown() {
  const shutdown = (signal: string) => {
    console.log(`\n🛑 Received ${signal}. Shutting down gracefully...`);

    // Perform cleanup tasks here
    // - Close database connections
    // - Finish processing requests
    // - Save any pending data

    console.log('✅ Graceful shutdown completed');
    process.exit(0);
  };

  // Handle different shutdown signals
  process.on('SIGTERM', () => shutdown('SIGTERM'));
  process.on('SIGINT', () => shutdown('SIGINT'));
  process.on('SIGUSR2', () => shutdown('SIGUSR2')); // For nodemon

  // Handle uncaught exceptions and rejections
  process.on('uncaughtException', error => {
    console.error('💥 Uncaught Exception:', error);
    process.exit(1);
  });

  process.on('unhandledRejection', (reason, promise) => {
    console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
  });
}
