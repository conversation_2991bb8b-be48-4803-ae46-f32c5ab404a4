import { supabase } from './supabase';

export interface FeaturedCampaignPayment {
  id: string;
  campaign_id: string;
  payment_type: 'featured_campaign';
  payment_amount: number;
  platform_fee: number;
  total_paid: number;
  currency: string;
  payment_status: 'pending' | 'completed' | 'failed';
  payment_completed_at?: string;
  featured_until?: string;
  stripe_session_id?: string;
  stripe_payment_intent_id?: string;
  created_at: string;
  updated_at: string;
}

export interface FeaturedCampaignPrice {
  days: 7 | 14;
  price: number;
  label: string;
}

// Cijene za featured kampanje
export const FEATURED_CAMPAIGN_PRICES: FeaturedCampaignPrice[] = [
  { days: 7, price: 30.0, label: '7 dana' },
  { days: 14, price: 50.0, label: '14 dana' },
];

// Interface types for database operations
interface CampaignInfo {
  id: string;
  title: string;
  status: string;
  business_id: string;
  is_featured: boolean;
}

interface PromotionInfo {
  id: string;
  campaign_id: string;
  payment_type: string;
  featured_until: string;
  payment_status: string;
  payment_amount: number;
  created_at: string;
}

interface PaymentRecord {
  id: string;
  campaign_id: string;
  payment_type: string;
  payment_amount: number;
  platform_fee: number;
  total_paid: number;
  currency: string;
  payment_status: string;
  featured_until: string;
  stripe_session_id?: string;
  created_at: string;
  updated_at: string;
}

/**
 * Proverava da li kampanja može biti promovirana
 * @param campaignId ID kampanje
 * @param businessId ID business korisnika
 * @returns da li kampanja može biti promovirana
 */
export async function canPromoteCampaign(
  campaignId: string,
  businessId: string
): Promise<{
  canPromote: boolean;
  reason?: string;
  campaign?: CampaignInfo;
}> {
  try {
    // Provjeri subscription type biznisa
    const { data: business, error: businessError } = await supabase
      .from('businesses')
      .select('subscription_type')
      .eq('id', businessId)
      .single();

    if (businessError || !business) {
      return { canPromote: false, reason: 'Greška pri provjeri korisnika' };
    }

    // Samo premium korisnici mogu promovirati kampanje
    if (business.subscription_type !== 'premium') {
      return {
        canPromote: false,
        reason: 'Samo Premium korisnici mogu promovirati kampanje',
      };
    }

    // Provjeri da li je kampanja aktivna i pripada business korisniku
    const { data: campaign, error } = await supabase
      .from('campaigns')
      .select('id, title, status, is_featured, business_id')
      .eq('id', campaignId)
      .eq('business_id', businessId)
      .single();

    if (error || !campaign) {
      return { canPromote: false, reason: 'Kampanja nije pronađena' };
    }

    if (campaign.status !== 'active') {
      return {
        canPromote: false,
        reason: 'Kampanja mora biti aktivna da bi mogla biti promovirana',
        campaign,
      };
    }

    // Edge Function će provjeriti da li kampanja već ima aktivnu promociju
    // Frontend ne treba da čita payments tabelu direktno
    return { canPromote: true, campaign };
  } catch (error) {
    console.error('Greška pri provjeri mogućnosti promocije:', error);
    return { canPromote: false, reason: 'Greška pri provjeri' };
  }
}

/**
 * Simulira plaćanje i promovira kampanju
 * @param campaignId ID kampanje
 * @param businessId ID business korisnika
 * @param durationDays broj dana promocije (7 ili 14)
 * @returns rezultat promocije
 */
export async function promoteCampaign(
  campaignId: string,
  businessId: string,
  durationDays: 7 | 14
): Promise<{
  success: boolean;
  error?: string;
  promotion?: PromotionInfo;
}> {
  try {
    // Provjeri da li kampanja može biti promovirana
    const { canPromote, reason } = await canPromoteCampaign(
      campaignId,
      businessId
    );
    if (!canPromote) {
      return { success: false, error: reason };
    }

    // Pronađi cijenu
    const priceConfig = FEATURED_CAMPAIGN_PRICES.find(
      p => p.days === durationDays
    );
    if (!priceConfig) {
      return { success: false, error: 'Nepoznata duracija' };
    }

    const now = new Date();
    const startsAt = now.toISOString();
    const endsAt = new Date(
      now.getTime() + durationDays * 24 * 60 * 60 * 1000
    ).toISOString();

    // Kreiraj payment record (simulacija plaćanja)
    const { data: payment, error: paymentError } = await supabase
      .from('payments')
      .insert({
        campaign_id: campaignId,
        payment_type: 'featured_campaign',
        payment_amount: priceConfig.price,
        platform_fee: 0,
        total_paid: priceConfig.price,
        currency: 'EUR',
        payment_status: 'completed', // U development odmah stavljamo kao plaćeno
        payment_completed_at: startsAt,
        featured_until: endsAt,
      })
      .select()
      .single();

    if (paymentError) {
      console.error('Greška pri kreiranju payment-a:', paymentError);
      return { success: false, error: 'Greška pri kreiranju payment-a' };
    }

    // Ažuriraj kampanju da bude featured
    const { error: campaignError } = await supabase
      .from('campaigns')
      .update({
        is_featured: true,
        featured_until: endsAt,
      })
      .eq('id', campaignId);

    if (campaignError) {
      console.error('Greška pri ažuriranju kampanje:', campaignError);
      // Pokušaj obrisati payment record ako kampanja update nije uspješan
      await supabase.from('payments').delete().eq('id', payment.id);

      return { success: false, error: 'Greška pri ažuriranju kampanje' };
    }

    return { success: true, promotion: payment };
  } catch (error) {
    console.error('Greška pri promociji kampanje:', error);
    return { success: false, error: 'Neočekivana greška' };
  }
}

/**
 * Dohvaća aktivne promocije za business
 * @param businessId ID business korisnika
 * @returns lista aktivnih promocija
 */
export async function getActivePromotions(): Promise<{
  data: PromotionInfo[] | null;
  error: unknown;
}> {
  try {
    const now = new Date().toISOString();

    const { data, error } = await supabase
      .from('payments')
      .select(
        `
        *,
        campaigns!inner(
          id,
          title,
          status
        )
      `
      )
      .eq('payment_type', 'featured_campaign')
      .eq('payment_status', 'completed')
      .gte('featured_until', now)
      .order('created_at', { ascending: false });

    return { data, error };
  } catch (error) {
    console.error('Greška pri dohvaćanju aktivnih promocija:', error);
    return { data: null, error };
  }
}

/**
 * Automatski uklanja featured status sa kampanja kojima je istekla promocija
 * Ova funkcija bi trebala biti pozvana periodically (cron job)
 */
export async function cleanupExpiredPromotions(): Promise<{
  success: boolean;
  expiredCount: number;
  error?: string;
}> {
  try {
    const now = new Date().toISOString();

    // Pronađi sve istekle promocije
    const { data: expiredPromotions, error: selectError } = await supabase
      .from('payments')
      .select('campaign_id')
      .eq('payment_type', 'featured_campaign')
      .eq('payment_status', 'completed')
      .lt('featured_until', now);

    if (selectError) {
      return {
        success: false,
        expiredCount: 0,
        error: 'Greška pri dohvaćanju isteklih promocija',
      };
    }

    if (!expiredPromotions || expiredPromotions.length === 0) {
      return { success: true, expiredCount: 0 };
    }

    // Ukloni featured status sa kampanja
    const campaignIds = expiredPromotions.map(
      (p: PromotionInfo) => p.campaign_id
    );
    const { error: updateError } = await supabase
      .from('campaigns')
      .update({
        is_featured: false,
        featured_until: null,
      })
      .in('id', campaignIds);

    if (updateError) {
      return {
        success: false,
        expiredCount: 0,
        error: 'Greška pri ažuriranju kampanja',
      };
    }

    return { success: true, expiredCount: expiredPromotions.length };
  } catch (error) {
    console.error('Greška pri čišćenju isteklih promocija:', error);
    return { success: false, expiredCount: 0, error: 'Neočekivana greška' };
  }
}

/**
 * Kreiranje Stripe checkout session za featured campaign promotion
 * @param campaignId ID kampanje
 * @param businessId ID business korisnika
 * @param durationDays Broj dana promocije (7 ili 14)
 * @returns Stripe session URL
 */
export async function createFeaturedCampaignPaymentSession(
  campaignId: string,
  businessId: string,
  durationDays: 7 | 14
) {
  try {
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) throw new Error('User not authenticated');

    const response = await fetch(
      '/api/stripe/create-featured-campaign-payment',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${session.access_token}`,
        },
        body: JSON.stringify({
          campaignId,
          businessId,
          durationDays,
        }),
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to create payment session');
    }

    return await response.json();
  } catch (error) {
    console.error('Error creating featured campaign payment session:', error);
    throw error;
  }
}

/**
 * Proverava da li je kampanja trenutno plaćena kao featured
 * @param campaignId ID kampanje
 * @returns da li je kampanja featured putem plaćanja
 */
export async function isCampaignFeaturedPaid(
  campaignId: string
): Promise<boolean> {
  try {
    const { data: payment } = await supabase
      .from('payments')
      .select('id, featured_until')
      .eq('campaign_id', campaignId)
      .eq('payment_type', 'featured_campaign')
      .eq('payment_status', 'completed')
      .single();

    if (!payment) return false;

    // Check if featured period is still active
    const endsAt = payment.featured_until;
    if (endsAt && new Date(endsAt) > new Date()) {
      return true;
    }

    return false;
  } catch (error) {
    console.error('Error checking featured campaign payment status:', error);
    return false;
  }
}

/**
 * Dobija payment info za featured campaign
 * @param campaignId ID kampanje
 * @returns payment informacije ili null
 */
export async function getFeaturedCampaignPaymentInfo(
  campaignId: string
): Promise<PaymentRecord | null> {
  try {
    const { data: payment, error } = await supabase
      .from('payments')
      .select('*')
      .eq('campaign_id', campaignId)
      .eq('payment_type', 'featured_campaign')
      .eq('payment_status', 'completed')
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle();

    if (error) {
      throw error;
    }

    return payment;
  } catch (error) {
    console.error('Error fetching featured campaign payment info:', error);
    return null;
  }
}
