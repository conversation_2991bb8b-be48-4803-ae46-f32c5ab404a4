'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Plus, Edit, Eye, Loader2, Play, Crown } from 'lucide-react';
import Link from 'next/link';

import {
  getBusinessCampaignsDashboardOptimized,
  getBusinessCampaignsDashboardStats,
} from '@/lib/campaigns';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { GradientTabs } from '@/components/ui/gradient-tabs';
import { GradientButton } from '@/components/ui/gradient-button';
import {
  canActivateCampaign,
  activateCampaign as activateCampaignFunction,
} from '@/lib/subscriptions';

interface Platform {
  name: string;
  icon: string;
  content_types: string[];
}

interface Campaign {
  id: string;
  title: string;
  description: string;
  budget: number;
  status: 'draft' | 'active' | 'paused' | 'completed' | 'cancelled';
  created_at: string;
  application_count: number;
  content_types: string[];
  platforms: Platform[];
  is_featured?: boolean;
}

interface CleanCampaignCardProps {
  campaign: Campaign;
  onActivate?: (id: string) => void;
  isActivating?: boolean;
  onPromote?: (campaign: {
    id: string;
    title: string;
    business_id?: string;
  }) => void;
  userSubscriptionType?: 'free' | 'premium';
}

// Nova statična CampaignRow komponenta
const StaticCampaignRow = ({
  campaign,
  onActivate,
  isActivating = false,
  onPromote,
}: CleanCampaignCardProps) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('sr-RS', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  return (
    <div
      className={`border-b border-gray-200 transition-all duration-200 ${
        campaign.is_featured ? 'bg-amber-50/30' : 'bg-white hover:bg-gray-50/30'
      }`}
    >
      {/* Static Row with Grid Layout */}
      <div className="grid grid-cols-12 gap-3 px-4 py-2.5 hover:bg-gray-50/50 transition-colors items-center border-b border-gray-100 last:border-b-0">
        <div className="col-span-2 text-sm text-gray-600">
          {formatDate(campaign.created_at)}
        </div>

        <div className="col-span-4 min-w-0 flex items-center gap-2">
          {campaign.is_featured && (
            <Crown className="w-4 h-4 text-amber-600 flex-shrink-0" />
          )}
          <div className="min-w-0">
            <h3 className="font-medium text-gray-900 truncate text-sm leading-tight">
              {campaign.title}
            </h3>
            <p className="text-xs text-gray-500 truncate leading-tight">
              {campaign.description}
            </p>
          </div>
        </div>

        <div className="col-span-2 text-sm text-gray-900 font-medium text-right">
          {campaign.budget?.toLocaleString() || '0'} €
        </div>

        <div className="col-span-2 text-sm text-gray-900 font-medium text-center">
          {campaign.application_count || 0}
        </div>

        {/* Action Buttons */}
        <div className="col-span-2">
          <div className="flex gap-1 justify-center">
            {/* Pregled button - always visible */}
            <Link href={`/campaigns/${campaign.id}`}>
              <Button
                variant="outline"
                size="sm"
                className="border-purple-200 text-purple-600 hover:bg-purple-50 bg-transparent p-1.5 min-w-0"
                title="Pregled"
              >
                <Eye className="w-3 h-3" />
              </Button>
            </Link>

            {/* Draft campaign buttons */}
            {campaign.status === 'draft' && (
              <>
                <Link href={`/campaigns/${campaign.id}/edit`}>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-pink-200 text-pink-600 hover:bg-pink-50 bg-transparent p-1.5 min-w-0"
                    title="Uredi"
                  >
                    <Edit className="w-3 h-3" />
                  </Button>
                </Link>

                {onActivate && (
                  <Button
                    size="sm"
                    className="bg-purple-600 hover:bg-purple-700 text-white border-0 p-1.5 min-w-0"
                    onClick={() => onActivate(campaign.id)}
                    disabled={isActivating}
                    title="Aktiviraj"
                  >
                    {isActivating ? (
                      <Loader2 className="w-3 h-3 animate-spin" />
                    ) : (
                      <Play className="w-3 h-3" />
                    )}
                  </Button>
                )}
              </>
            )}

            {/* Active campaign promote button - fixed condition */}
            {campaign.status === 'active' &&
              !campaign.is_featured &&
              onPromote && (
                <Button
                  variant="outline"
                  size="sm"
                  className="border-amber-300 text-amber-700 hover:bg-amber-50 bg-transparent p-1.5 min-w-0"
                  onClick={() =>
                    onPromote({
                      id: campaign.id,
                      title: campaign.title,
                      business_id: campaign.business_id || '',
                    })
                  }
                  title="Promoviši"
                >
                  <Crown className="w-3 h-3" />
                </Button>
              )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default function TestCampaignsPage() {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('draft');
  const [stats, setStats] = useState({
    draft: 0,
    active: 0,
    featured: 0,
  });
  const [activatingCampaign, setActivatingCampaign] = useState<string | null>(
    null
  );

  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/prijava');
      return;
    }

    if (user) {
      loadCampaigns();
    }
  }, [user, authLoading, router, activeTab]);

  const loadCampaigns = async () => {
    try {
      setLoading(true);

      if (!user?.id) {
        return;
      }

      let statusFilter: string;
      if (activeTab === 'featured') {
        statusFilter = 'active';
      } else {
        statusFilter = activeTab === 'draft' ? 'draft' : 'active';
      }

      const [campaignsResult, statsResult] = await Promise.all([
        getBusinessCampaignsDashboardOptimized(user.id, statusFilter),
        getBusinessCampaignsDashboardStats(user.id),
      ]);

      if (campaignsResult.error) {
        console.error('Error loading campaigns:', campaignsResult.error);
        return;
      }

      if (statsResult.error) {
        console.error('Error loading stats:', statsResult.error);
      } else if (statsResult.data) {
        setStats({
          draft: statsResult.data.draft_count,
          active:
            statsResult.data.active_count - statsResult.data.featured_count,
          featured: statsResult.data.featured_count,
        });
      }

      let transformedCampaigns = (campaignsResult.data || []).map(campaign => ({
        ...campaign,
        platforms: Array.isArray(campaign.platforms)
          ? campaign.platforms.map((p: Platform) => ({
              name: p.name,
              icon: p.icon,
              content_types: p.content_types || [],
            }))
          : [],
      }));

      if (activeTab === 'featured') {
        transformedCampaigns = transformedCampaigns.filter(
          campaign => campaign.is_featured
        );
      } else if (activeTab === 'active') {
        transformedCampaigns = transformedCampaigns.filter(
          campaign => !campaign.is_featured
        );
      }

      setCampaigns(transformedCampaigns);
    } catch (error) {
      console.error('Error loading campaigns:', error);
    } finally {
      setLoading(false);
    }
  };

  const activateCampaign = async (campaignId: string) => {
    if (!user?.id) return;

    try {
      setActivatingCampaign(campaignId);

      // Check subscription limits before activation
      const activationCheck = await canActivateCampaign(user.id);

      if (!activationCheck.canActivate) {
        console.log('Cannot activate campaign - reached limits');
        return;
      }

      // Use our activation function
      const success = await activateCampaignFunction(campaignId, user.id);

      if (!success) {
        console.error('Failed to activate campaign');
        return;
      }

      // Refresh campaigns
      await loadCampaigns();
    } catch (error) {
      console.error('Error activating campaign:', error);
    } finally {
      setActivatingCampaign(null);
    }
  };

  const handlePromoteCampaign = (campaign: {
    id: string;
    title: string;
    business_id?: string;
  }) => {
    console.log('Promoting campaign:', campaign);
    // Here you would typically open a promotion modal
  };

  if (authLoading || loading) {
    return (
      <DashboardLayout requiredUserType="business">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="flex items-center gap-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Loading campaigns...</span>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredUserType="business">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold">
              Kampanje - Tabela
            </h1>
            <p className="text-muted-foreground mt-1">
              Kampanje prikazane kao čista tabela sa osnovnim podacima
            </p>
          </div>

          <Link href="/campaigns/create">
            <GradientButton
              gradientVariant="primary"
              className="w-full sm:w-auto"
            >
              <Plus className="h-4 w-4" />
              Nova Kampanja
            </GradientButton>
          </Link>
        </div>

        {/* Campaigns Tabs */}
        <div className="space-y-6">
          <GradientTabs
            tabs={[
              { name: 'Neaktivne', value: 'draft', count: stats.draft },
              { name: 'Aktivne', value: 'active', count: stats.active },
              { name: 'Promovisane', value: 'featured', count: stats.featured },
            ]}
            activeTab={activeTab}
            onTabChange={setActiveTab}
          />

          {/* Tab Content - Novi grid sa manjim kartama */}
          <div className="mt-6">
            {campaigns.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-muted-foreground mb-4">
                  {activeTab === 'draft'
                    ? 'Nemate neaktivnih kampanja'
                    : 'Nemate aktivnih kampanja'}
                </p>
                {activeTab === 'draft' && (
                  <Link href="/campaigns/create">
                    <GradientButton gradientVariant="primary">
                      <Plus className="h-4 w-4" />
                      Kreiraj prvu kampanju
                    </GradientButton>
                  </Link>
                )}
              </div>
            ) : (
              <div className="space-y-0">
                {/* Header Row with Column Names */}
                <div className="grid grid-cols-12 gap-3 px-4 py-2.5 bg-gray-50 border border-gray-200 rounded-t-lg font-medium text-xs text-gray-600 uppercase tracking-wide">
                  <div className="col-span-2">Datum kreiranja</div>
                  <div className="col-span-4">Naziv</div>
                  <div className="col-span-2 text-right">Budžet</div>
                  <div className="col-span-2 text-center">Prijave</div>
                  <div className="col-span-2 text-center">Akcije</div>
                </div>

                {/* Campaign Items */}
                <div className="border-x border-b border-gray-200 rounded-b-lg">
                  {campaigns.map(campaign => (
                    <StaticCampaignRow
                      key={campaign.id}
                      campaign={campaign}
                      onActivate={activateCampaign}
                      isActivating={activatingCampaign === campaign.id}
                      onPromote={handlePromoteCampaign}
                      userSubscriptionType="free"
                    />
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
