'use client';

import { useEffect, useState, useCallback } from 'react';
import { usePara<PERSON>, useRouter, useSearchParams } from 'next/navigation';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Clock,
  CheckCircle,
  XCircle,
  Calendar,
  Euro,
  MessageCircle,
  User,
  FileText,
  Tag,
  CreditCard,
} from 'lucide-react';
import PlatformIconSimple from '@/components/ui/platform-icon-simple';
import { BackButton } from '@/components/ui/back-button';
import { getDirectOffer, type DirectOfferWithDetails } from '@/lib/offers';
import { formatDistanceToNow } from 'date-fns';
import { hr } from 'date-fns/locale';
import Link from 'next/link';
import {
  getJobCompletionByDirectOffer,
  type JobCompletion,
} from '@/lib/job-completions';
import { toast } from 'sonner';
import { ApproveJobModal } from '@/components/job-completion/ApproveJobModal';
import { RejectJobModal } from '@/components/job-completion/RejectJobModal';
import { OfferPaymentButton } from '@/components/offers/OfferPaymentButton';
import { formatDate } from '@/lib/date-utils';
import { getDisplayName, getInitials } from '@/lib/utils';
import { getOfferPaymentInfo } from '@/lib/offers';

export default function OfferDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [offer, setOffer] = useState<DirectOfferWithDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [jobCompletion, setJobCompletion] = useState<JobCompletion | null>(
    null
  );
  const [isLoadingJobCompletion, setIsLoadingJobCompletion] = useState(false);
  const [showApproveModal, setShowApproveModal] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [paymentInfo, setPaymentInfo] = useState<{
    total_paid: number;
    platform_fee: number;
    payment_amount: number;
  } | null>(null);

  useEffect(() => {
    if (params.id) {
      loadOffer(params.id as string);
      loadJobCompletion(params.id as string);
    }
  }, [params.id, loadOffer, loadJobCompletion]);

  // Check for payment success parameter
  useEffect(() => {
    const paymentStatus = searchParams.get('payment');
    if (paymentStatus === 'success' && offer) {
      handlePaymentSuccess();

      // Clean URL params
      const url = new URL(window.location.href);
      url.searchParams.delete('payment');
      url.searchParams.delete('session_id');
      window.history.replaceState({}, '', url.pathname);
    }
  }, [searchParams, offer, handlePaymentSuccess]);

  const loadOffer = useCallback(
    async (offerId: string) => {
      setIsLoading(true);
      try {
        const { data, error } = await getDirectOffer(offerId);
        if (error) {
          console.error('Error loading offer:', error);
          router.push('/dashboard/biznis/offers');
        } else {
          setOffer(data);

          // Load payment info if offer is accepted
          if (data && data.status === 'accepted') {
            const paymentData = await getOfferPaymentInfo(data.id);
            setPaymentInfo(paymentData);
          }
        }
      } catch (error) {
        console.error('Error loading offer:', error);
        router.push('/dashboard/biznis/offers');
      } finally {
        setIsLoading(false);
      }
    },
    [router]
  );

  const loadJobCompletion = useCallback(async (offerId: string) => {
    setIsLoadingJobCompletion(true);
    try {
      const { data, error } = await getJobCompletionByDirectOffer(offerId);
      if (error) {
        console.error('Error loading job completion:', error);
      } else {
        setJobCompletion(data);
      }
    } catch (error) {
      console.error('Error loading job completion:', error);
    } finally {
      setIsLoadingJobCompletion(false);
    }
  }, []);

  const handlePaymentSuccess = useCallback(async () => {
    if (!offer) return;

    // Show success message
    toast.success('Plaćanje uspešno završeno!');

    // Reload offer data to get updated payment status
    await loadOffer(offer.id);
  }, [offer, loadOffer]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-800 border-yellow-200';
      case 'accepted':
        return 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border-green-200';
      case 'rejected':
        return 'bg-gradient-to-r from-red-100 to-pink-100 text-red-800 border-red-200';
      case 'completed':
        return 'bg-gradient-to-r from-blue-100 to-cyan-100 text-blue-800 border-blue-200';
      case 'cancelled':
        return 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'accepted':
        return <CheckCircle className="h-4 w-4" />;
      case 'rejected':
        return <XCircle className="h-4 w-4" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Na čekanju';
      case 'accepted':
        return 'Prihvaćeno';
      case 'rejected':
        return 'Odbijeno';
      case 'completed':
        return 'Završeno';
      case 'cancelled':
        return 'Otkazano';
      default:
        return status;
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout requiredUserType="business">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (!offer) {
    return (
      <DashboardLayout requiredUserType="business">
        <div className="space-y-6">
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold mb-4 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              Ponuda nije pronađena
            </h2>
            <Link href="/dashboard/biznis/offers">
              <BackButton>Nazad na ponude</BackButton>
            </Link>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredUserType="business">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <div className="hidden md:block">
            <Link href="/dashboard/biznis/offers">
              <BackButton />
            </Link>
          </div>
          <div className="flex-1">
            <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              {offer.title}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Detalji ponude poslane{' '}
              {formatDistanceToNow(new Date(offer.created_at), {
                addSuffix: true,
                locale: hr,
              })}
            </p>
          </div>
          <Badge
            variant="outline"
            className={`${getStatusColor(offer.status)} font-medium`}
          >
            <div className="flex items-center space-x-1">
              {getStatusIcon(offer.status)}
              <span>{getStatusText(offer.status)}</span>
            </div>
          </Badge>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Offer Overview */}
            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
              <div className="relative p-6 space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <FileText className="h-5 w-5 text-purple-500" />
                  <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                    Pregled ponude
                  </h3>
                </div>

                <div>
                  <h4 className="font-medium text-lg text-gray-900 dark:text-gray-100 mb-2">
                    {offer.title}
                  </h4>
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                    {offer.description}
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
                    <div className="flex items-center gap-2">
                      <Euro className="h-4 w-4 text-purple-500" />
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        Budžet:
                      </span>
                      <span className="font-semibold text-gray-900 dark:text-gray-100">
                        {offer.budget.toLocaleString()} €
                      </span>
                    </div>
                  </div>

                  {offer.deadline && (
                    <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-purple-500" />
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          Rok:
                        </span>
                        <span className="font-semibold text-gray-900 dark:text-gray-100">
                          {formatDate(offer.deadline)}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Platforms and Content Types */}
            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
              <div className="relative p-6">
                <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">
                  Platforme i tipovi sadržaja
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                  Gde influencer treba da objavi sadržaj i koji tip sadržaja
                </p>
                <div className="space-y-3">
                  {offer.platforms
                    .map((platform, platformIndex) => {
                      // Filter content types that belong to this platform
                      const platformContentTypes = offer.content_types.filter(
                        type => {
                          const lowerType = type.toLowerCase();
                          const lowerPlatform = platform.toLowerCase();

                          // First check: exact platform name match in content type
                          if (lowerType.includes(lowerPlatform)) {
                            return true;
                          }

                          // Second check: platform-specific content type mappings
                          // Only match if the content type doesn't already mention another platform
                          const mentionsOtherPlatform =
                            (lowerPlatform !== 'instagram' &&
                              lowerType.includes('instagram')) ||
                            (lowerPlatform !== 'tiktok' &&
                              lowerType.includes('tiktok')) ||
                            (lowerPlatform !== 'youtube' &&
                              lowerType.includes('youtube')) ||
                            (lowerPlatform !== 'facebook' &&
                              lowerType.includes('facebook')) ||
                            (lowerPlatform !== 'twitter' &&
                              lowerType.includes('twitter'));

                          if (mentionsOtherPlatform) {
                            return false;
                          }

                          // Platform-specific generic mappings (only if no other platform is mentioned)
                          if (lowerPlatform === 'instagram') {
                            return (
                              lowerType.includes('post') ||
                              lowerType.includes('story') ||
                              lowerType.includes('reel') ||
                              lowerType.includes('photo')
                            );
                          }
                          if (lowerPlatform === 'tiktok') {
                            return (
                              lowerType.includes('video') ||
                              lowerType.includes('short')
                            );
                          }
                          if (lowerPlatform === 'youtube') {
                            return (
                              lowerType.includes('video') ||
                              lowerType.includes('short')
                            );
                          }
                          if (lowerPlatform === 'facebook') {
                            return (
                              lowerType.includes('post') ||
                              lowerType.includes('video')
                            );
                          }
                          if (lowerPlatform === 'twitter') {
                            return (
                              lowerType.includes('tweet') ||
                              lowerType.includes('post')
                            );
                          }

                          return false;
                        }
                      );

                      // Only show platform if it has content types
                      if (platformContentTypes.length === 0) return null;

                      return (
                        <div
                          key={platformIndex}
                          className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30"
                        >
                          <div className="flex items-center gap-2 mb-2">
                            <PlatformIconSimple platform={platform} size="lg" />
                            <span className="font-medium text-gray-800 dark:text-gray-200 text-sm">
                              {platform}
                            </span>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {platformContentTypes.map((type, typeIndex) => (
                              <Badge
                                key={typeIndex}
                                variant="secondary"
                                className="text-xs bg-gradient-to-r from-violet-100 to-purple-100 text-violet-700 border-violet-200"
                              >
                                {type === 'post'
                                  ? 'Photo Feed Post'
                                  : type === 'video'
                                    ? 'Video'
                                    : type === 'story'
                                      ? 'Story'
                                      : type === 'reel'
                                        ? 'Reel'
                                        : type === 'blog'
                                          ? 'Blog Post'
                                          : type}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      );
                    })
                    .filter(Boolean)}
                </div>
              </div>
            </div>

            {/* Requirements */}
            {offer.requirements && (
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <Tag className="h-5 w-5 text-purple-500" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Specifični zahtjevi
                    </h3>
                  </div>
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                    {offer.requirements}
                  </p>
                </div>
              </div>
            )}

            {/* Business Message */}
            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
              <div className="relative p-6">
                <div className="flex items-center gap-2 mb-4">
                  <MessageCircle className="h-5 w-5 text-purple-500" />
                  <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                    Vaša poruka
                  </h3>
                </div>
                <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-4 border border-purple-100/50 dark:border-purple-800/30">
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                    {offer.business_message}
                  </p>
                </div>
              </div>
            </div>

            {/* Influencer Response */}
            {offer.influencer_response && (
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <User className="h-5 w-5 text-purple-500" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Poruka Influencera
                    </h3>
                  </div>
                  <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-4 border border-purple-100/50 dark:border-purple-800/30">
                    <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                      {offer.influencer_response}
                    </p>
                  </div>
                  <div className="mt-4 text-sm text-gray-600 dark:text-gray-400">
                    {offer.status === 'accepted' && offer.accepted_at && (
                      <p>
                        Prihvaćeno{' '}
                        {formatDistanceToNow(new Date(offer.accepted_at), {
                          addSuffix: true,
                          locale: hr,
                        })}
                      </p>
                    )}
                    {offer.status === 'rejected' && offer.rejected_at && (
                      <p>
                        Odbijeno{' '}
                        {formatDistanceToNow(new Date(offer.rejected_at), {
                          addSuffix: true,
                          locale: hr,
                        })}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Influencer Info */}
            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
              <div className="relative p-6">
                <div className="flex items-center gap-2 mb-4">
                  <User className="h-5 w-5 text-purple-500" />
                  <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                    Influencer
                  </h3>
                </div>

                <div className="flex items-center gap-3 mb-4">
                  <Avatar className="h-16 w-16 border-2 border-white/50">
                    <AvatarImage
                      src={offer.influencer?.avatar_url || ''}
                      alt={getDisplayName(offer.influencer)}
                    />
                    <AvatarFallback className="text-lg bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700">
                      {getInitials(getDisplayName(offer.influencer))}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h4 className="font-semibold text-lg text-gray-900 dark:text-gray-100">
                      {getDisplayName(offer.influencer)}
                    </h4>
                    <p className="text-gray-600 dark:text-gray-400">
                      @{offer.influencer?.username}
                    </p>
                  </div>
                </div>

                <Link href={`/influencer/${offer.influencer.username}`}>
                  <button className="w-full flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium bg-white/70 dark:bg-gray-800/50 hover:bg-white dark:hover:bg-gray-800/70 border border-gray-200/50 dark:border-gray-700/50 rounded-lg transition-all duration-200 hover:shadow-md">
                    <User className="h-4 w-4" />
                    Pogledaj profil
                  </button>
                </Link>
              </div>
            </div>

            {/* Payment Section */}
            {offer.status === 'accepted' && !paymentInfo && (
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-amber-50/80 via-orange-50/60 to-amber-100/80 dark:from-amber-950/20 dark:via-orange-950/10 dark:to-amber-900/30 border border-amber-200/50 dark:border-amber-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-amber-100/30 via-orange-100/20 to-amber-200/40 dark:from-amber-900/10 dark:via-orange-900/5 dark:to-amber-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <CreditCard className="h-5 w-5 text-amber-600" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent">
                      Plaćanje potrebno
                    </h3>
                  </div>

                  <div className="space-y-4">
                    <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-4 border border-amber-100/50 dark:border-amber-800/30">
                      <p className="text-gray-700 dark:text-gray-300 mb-3">
                        Influencer je prihvatio vašu ponudu! Da biste počeli
                        komunikaciju i rad na projektu, potrebno je da izvršite
                        plaćanje.
                      </p>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600 dark:text-gray-400">
                          Budžet ponude:
                        </span>
                        <span className="font-semibold text-gray-900 dark:text-gray-100">
                          {offer.budget.toLocaleString()} €
                        </span>
                      </div>
                    </div>

                    <OfferPaymentButton
                      offerId={offer.id}
                      budget={offer.budget}
                      onPaymentSuccess={handlePaymentSuccess}
                      className="w-full bg-gradient-to-r from-amber-500 via-orange-500 to-amber-600 hover:from-amber-600 hover:via-orange-600 hover:to-amber-700 text-white"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Payment Confirmation */}
            {offer.status === 'accepted' && paymentInfo && (
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-green-50/80 via-emerald-50/60 to-green-100/80 dark:from-green-950/20 dark:via-emerald-950/10 dark:to-green-900/30 border border-green-200/50 dark:border-green-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-green-100/30 via-emerald-100/20 to-green-200/40 dark:from-green-900/10 dark:via-emerald-900/5 dark:to-green-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                      Plaćanje završeno
                    </h3>
                  </div>

                  <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-4 border border-green-100/50 dark:border-green-800/30">
                    <p className="text-green-700 dark:text-green-300 mb-3">
                      ✅ Uspešno ste platili ponudu. Sada možete komunicirati sa
                      influencerom i početi rad na projektu.
                    </p>
                    {paymentInfo && (
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-green-600 dark:text-green-400">
                          Plaćeno:
                        </span>
                        <span className="font-semibold text-green-900 dark:text-green-100">
                          {paymentInfo?.total_paid?.toLocaleString()} €
                          <span className="text-xs text-green-600 dark:text-green-400 ml-1">
                            (uključujući 10% proviziju)
                          </span>
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Chat Communication */}
            {offer.status === 'accepted' && paymentInfo && (
              <div className="space-y-4">
                {/* Chat Button */}
                <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                  <div className="relative p-6">
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
                      Komunikacija
                    </h3>
                    <Button
                      onClick={() => {
                        window.location.href = `/dashboard/chat?offer=${offer.id}`;
                      }}
                      className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-0"
                    >
                      <MessageCircle className="h-4 w-4 mr-2" />
                      Otvori chat
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* Job Completion Section */}
            {offer.status === 'accepted' && paymentInfo && (
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <FileText className="h-5 w-5 text-purple-500" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Završetak posla
                    </h3>
                  </div>

                  {isLoadingJobCompletion ? (
                    <div className="flex items-center justify-center p-8">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    </div>
                  ) : jobCompletion ? (
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <Badge
                          variant={
                            jobCompletion.status === 'submitted'
                              ? 'secondary'
                              : jobCompletion.status === 'approved'
                                ? 'default'
                                : jobCompletion.status === 'rejected'
                                  ? 'destructive'
                                  : 'outline'
                          }
                        >
                          {jobCompletion.status === 'submitted' &&
                            'Poslano na pregled'}
                          {jobCompletion.status === 'approved' && 'Odobreno'}
                          {jobCompletion.status === 'rejected' && 'Odbačeno'}
                          {jobCompletion.status === 'pending' && 'Na čekanju'}
                        </Badge>
                        {jobCompletion.submitted_at && (
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            Poslano{' '}
                            {formatDistanceToNow(
                              new Date(jobCompletion.submitted_at),
                              { addSuffix: true, locale: hr }
                            )}
                          </span>
                        )}
                      </div>

                      {jobCompletion.submission_notes && (
                        <div>
                          {(() => {
                            try {
                              const submissionData = JSON.parse(
                                jobCompletion.submission_notes
                              );
                              return (
                                <div className="space-y-3">
                                  {/* Post Links */}
                                  {submissionData.post_links &&
                                    submissionData.post_links.length > 0 && (
                                      <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
                                        <div className="flex items-center gap-2 mb-3">
                                          <svg
                                            className="h-4 w-4 text-blue-600"
                                            fill="none"
                                            stroke="currentColor"
                                            viewBox="0 0 24 24"
                                          >
                                            <path
                                              strokeLinecap="round"
                                              strokeLinejoin="round"
                                              strokeWidth={2}
                                              d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                                            />
                                          </svg>
                                          <h4 className="font-medium text-gray-900 dark:text-gray-100">
                                            Objavljeni postovi:
                                          </h4>
                                        </div>
                                        <div className="space-y-2">
                                          {submissionData.post_links.map(
                                            (link: string, index: number) => (
                                              <div
                                                key={index}
                                                className="bg-gradient-to-r from-blue-50 to-indigo-50 p-3 rounded-lg border border-blue-100"
                                              >
                                                <a
                                                  href={link}
                                                  target="_blank"
                                                  rel="noopener noreferrer"
                                                  className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center gap-2 hover:underline"
                                                >
                                                  <svg
                                                    className="h-3 w-3"
                                                    fill="none"
                                                    stroke="currentColor"
                                                    viewBox="0 0 24 24"
                                                  >
                                                    <path
                                                      strokeLinecap="round"
                                                      strokeLinejoin="round"
                                                      strokeWidth={2}
                                                      d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                                                    />
                                                  </svg>
                                                  {link}
                                                </a>
                                              </div>
                                            )
                                          )}
                                        </div>
                                      </div>
                                    )}

                                  {/* Optional Message */}
                                  {submissionData.message && (
                                    <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
                                      <div className="flex items-center gap-2 mb-3">
                                        <MessageCircle className="h-4 w-4 text-green-600" />
                                        <h4 className="font-medium text-gray-900 dark:text-gray-100">
                                          Poruka influencera:
                                        </h4>
                                      </div>
                                      <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-3 rounded-lg border border-green-100">
                                        <p className="text-sm text-gray-700">
                                          {submissionData.message}
                                        </p>
                                      </div>
                                    </div>
                                  )}
                                </div>
                              );
                            } catch {
                              // Fallback for old format
                              return (
                                <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
                                  <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                                    Napomene influencera:
                                  </h4>
                                  <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                                    {jobCompletion.submission_notes}
                                  </p>
                                </div>
                              );
                            }
                          })()}
                        </div>
                      )}

                      {jobCompletion.submission_files && (
                        <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
                          <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                            Prilozi:
                          </h4>
                          <div className="text-sm text-gray-700 dark:text-gray-300">
                            {JSON.parse(jobCompletion.submission_files).length}{' '}
                            fajl(ova) priloženo
                          </div>
                        </div>
                      )}

                      {jobCompletion.status === 'submitted' && (
                        <div className="flex gap-2 pt-4">
                          <button
                            onClick={() => setShowApproveModal(true)}
                            className="flex-1 flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium text-white bg-gradient-to-r from-green-400 via-emerald-400 to-green-500 hover:from-green-500 hover:via-emerald-500 hover:to-green-600 rounded-lg transition-all duration-200 hover:shadow-lg"
                          >
                            <CheckCircle className="h-4 w-4" />
                            Odobri rad
                          </button>
                          <button
                            onClick={() => setShowRejectModal(true)}
                            className="flex-1 flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium text-white bg-gradient-to-r from-red-400 via-pink-400 to-red-500 hover:from-red-500 hover:via-pink-500 hover:to-red-600 rounded-lg transition-all duration-200 hover:shadow-lg"
                          >
                            <XCircle className="h-4 w-4" />
                            Odbaci rad
                          </button>
                        </div>
                      )}

                      {jobCompletion.business_notes && (
                        <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
                          <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                            Vaše napomene:
                          </h4>
                          <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                            {jobCompletion.business_notes}
                          </p>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-700 dark:text-gray-300">
                        Influencer još uvijek nije poslao završetak posla
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Timeline */}
            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
              <div className="relative p-6">
                <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
                  Istorija
                </h3>

                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        Ponuda poslana
                      </p>
                      <p className="text-xs text-gray-600 dark:text-gray-400">
                        {formatDistanceToNow(new Date(offer.created_at), {
                          addSuffix: true,
                          locale: hr,
                        })}
                      </p>
                    </div>
                  </div>

                  {offer.status === 'accepted' && offer.accepted_at && (
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          Ponuda prihvaćena
                        </p>
                        <p className="text-xs text-gray-600 dark:text-gray-400">
                          {formatDistanceToNow(new Date(offer.accepted_at), {
                            addSuffix: true,
                            locale: hr,
                          })}
                        </p>
                      </div>
                    </div>
                  )}

                  {offer.status === 'rejected' && offer.rejected_at && (
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          Ponuda odbijena
                        </p>
                        <p className="text-xs text-gray-600 dark:text-gray-400">
                          {formatDistanceToNow(new Date(offer.rejected_at), {
                            addSuffix: true,
                            locale: hr,
                          })}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modals */}
      {jobCompletion && (
        <>
          <ApproveJobModal
            isOpen={showApproveModal}
            onClose={() => setShowApproveModal(false)}
            onSuccess={() => {
              loadJobCompletion(params.id as string);
              setShowApproveModal(false);
            }}
            jobCompletionId={jobCompletion.id}
            influencerName={
              offer?.influencer?.full_name ||
              offer?.influencer?.public_display_name ||
              offer?.influencer?.username ||
              'Influencer'
            }
          />

          <RejectJobModal
            isOpen={showRejectModal}
            onClose={() => setShowRejectModal(false)}
            onSuccess={() => {
              loadJobCompletion(params.id as string);
              setShowRejectModal(false);
            }}
            jobCompletionId={jobCompletion.id}
            influencerName={
              offer?.influencer?.full_name ||
              offer?.influencer?.public_display_name ||
              offer?.influencer?.username ||
              'Influencer'
            }
          />
        </>
      )}
    </DashboardLayout>
  );
}
